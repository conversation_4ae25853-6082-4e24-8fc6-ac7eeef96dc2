from ultralytics import YOLO
import cv2
import xgboost as xgb
import pandas as pd
import serial
import time

arduino = serial.Serial('COM15', 9600) 

model_yolo = YOLO(r'C:\Users\<USER>\Downloads\poseshekhar\yolov8n-pose.pt')
model = xgb.Booster()
model.load_model(r'C:\Users\<USER>\face recognition mod\model_weights.xgb')

confidence_threshold = 0.95
#video_path = r"C:\Users\<USER>\Downloads\poseshekhar\WIN_20240509_12_31_54_Pro.mp4"
cap = cv2.VideoCapture(2)

print('Total Frame', cap.get(cv2.CAP_PROP_FRAME_COUNT))

# Get video properties
fps = int(cap.get(cv2.CAP_PROP_FPS))
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

frame_tot = 0
# Loop through the video frames
while cap.isOpened():
    # Read a frame from the video
    success, frame = cap.read()

    if success:
        # Run YOLOv8 inference on the frame
        results = model_yolo(frame, verbose = False)

        # Visualize the results on the frame
        annotated_frame = results[0].plot(boxes = False)

        for r in results:
            bound_box = r.boxes.xyxy
            conf = r.boxes.conf.tolist()
            keypoints = r.keypoints.xyn.tolist()

            for index, box in enumerate(bound_box):
                if conf[index] > 0.9:
                    x1, y1, x2, y2 = box.tolist()
                    data = {}

                    # Initialize the x and y lists for each possible key
                    for j in range(len(keypoints[index])):
                        data[f'x{j}'] = keypoints[index][j][0]
                        data[f'y{j}'] = keypoints[index][j][1]

                    df = pd.DataFrame(data, index=[0])
                    dmatrix = xgb.DMatrix(df)
                    cut = model.predict(dmatrix)
                    binary_predictions = (cut > 0.01).astype(int)
                    if binary_predictions == 0:
                        cv2.rectangle(annotated_frame, (int(x1), int(y1)), (int(x2), int(y2)), (255, 0, 0), 2)
                        cv2.putText(annotated_frame, 'handshaking', (int(x1), int(y1)), cv2.FONT_HERSHEY_DUPLEX, 1.0, (255,0,0), 3)
                        arduino.write(b'H')
                        time.sleep(5)  # Sleep for 5 seconds between detections

        # Display the annotated frame
        cv2.imshow("Annotated Frame", annotated_frame)

        frame_tot += 1

        # Break the loop if 'q' is pressed
        if cv2.waitKey(1) & 0xFF == ord("q"):
            break
    else:
        # Break the loop if the end of the video is reached
        break

# Release the video capture object and close the display window
cap.release()
cv2.destroyAllWindows()
arduino.close()
