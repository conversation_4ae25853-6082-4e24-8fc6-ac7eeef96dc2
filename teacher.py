import openai
import pyttsx3
import speech_recognition as sr
import webbrowser
import face_recognition
import cv2
import numpy as np
import os
from datetime import datetime
import json
from keras.models import model_from_json
from utils import detect_object
from imagine_try import StoryProcessor
import serial
import time


from gemtakepicturethroughnlp import capture_and_process_image

# Set up OpenAI API key
openai.api_key = "********************************************************"

# Initialize the serial connection
#ser = serial.Serial('COM8', 9600)  # Update 'COM3' with your Arduino's port

# OpenAI completion instance
completion = openai.Completion()

# Function to get AI response to a question
def Reply(question):
    # First, check if the question exists in the JSON file
    with open(r'C:\Users\<USER>\OneDrive\Desktop\teacher\normal_chat.json', 'r') as f:
        chats = json.load(f)

    if question in chats:
        return chats[question]

    # If not found in JSON, query OpenAI
    prompt = f'name : {question}\n Jarvis: '
    response = completion.create(prompt=prompt, engine="gpt-3.5-turbo-instruct", stop=['\name'], max_tokens=200)
    answer = response.choices[0].text.strip()

    return answer 

# Initialize text-to-speech engine
engine = pyttsx3.init()
voices = engine.getProperty('voices')
rate = engine.getProperty('rate')
engine.setProperty('voice', voices[1].id)
engine.setProperty('rate',120)

# Function to speak a given text
def speak(text):
    engine.say(text)
    engine.runAndWait()

def move_servo(angle):
    #ser.write(str(angle).encode())  # Send angle as string over serial
    print(f"Moving servo to {angle} degrees...")
    time.sleep(1)  # Wait for servo to reach position    

# Function to recognize face and return the name
def recognize_face(frame, known_face_encodings, known_face_names):

    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    face_locations = face_recognition.face_locations(rgb_frame)
    face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)

    for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
        matches = face_recognition.compare_faces(known_face_encodings, face_encoding)
        name = "unknown"
        face_distances = face_recognition.face_distance(known_face_encodings, face_encoding)
        best_match_index = np.argmin(face_distances)
        if matches[best_match_index]:
            name = known_face_names[best_match_index]
        return name
def detect_emotion(face_img, model):
    face_gray = cv2.cvtColor(face_img, cv2.COLOR_BGR2GRAY)
    face_gray_resized = cv2.resize(face_gray, (48, 48))
    face_gray_normalized = face_gray_resized / 255.0
    face_gray_normalized = np.expand_dims(face_gray_normalized, axis=0)
    emotion_pred = model.predict(face_gray_normalized)
    return emotion_pred.argmax()

def highlightFace(net, frame, conf_threshold=0.7):
    frameOpencvDnn=frame.copy()
    frameHeight=frameOpencvDnn.shape[0]
    frameWidth=frameOpencvDnn.shape[1]
    blob=cv2.dnn.blobFromImage(frameOpencvDnn, 1.0, (300, 300), [104, 117, 123], True, False)

    net.setInput(blob)
    detections=net.forward()
    faceBoxes=[]
    for i in range(detections.shape[2]):
        confidence=detections[0,0,i,2]
        if confidence>conf_threshold:
            x1=int(detections[0,0,i,3]*frameWidth)
            y1=int(detections[0,0,i,4]*frameHeight)
            x2=int(detections[0,0,i,5]*frameWidth)
            y2=int(detections[0,0,i,6]*frameHeight)
            faceBoxes.append([x1,y1,x2,y2])
            cv2.rectangle(frameOpencvDnn, (x1,y1), (x2,y2), (0,255,0), int(round(frameHeight/150)), 8)
    return frameOpencvDnn,faceBoxes

# Function to save conversation by the name of the user in question and answer manner
def save_conversation(name, query, response, emotion, age, gender):
    file_path = f"{name}_conversation.txt"
    with open(file_path, "a") as file:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        file.write(f"{timestamp} - {name}: {query}\n")
        file.write(f"{timestamp} - Jarvis: {response}\n")
        file.write(f"{timestamp} - Emotion: {emotion}\n")
        file.write(f"{timestamp} - Age: {age}\n")
        file.write(f"{timestamp} - Gender: {gender}\n")
# Function to handle unknown face
def handle_unknown_face(frame):
    # Ask for name using speech recognition
    speak("you seem unknown to me. Please tell me your name.")
    name = takeCommand().lower()
    
    # Save the image with the detected name
    if name != "known":
        cv2.imwrite(os.path.join(faces_directory, f"{name}.jpg"), frame)
        speak(f"Nice to meet you, {name}! Your face has been saved.")
        # Update known_face_encodings and known_face_names with the newly saved face
        face_image = face_recognition.load_image_file(os.path.join(faces_directory, f"{name}.jpg"))
        face_encoding = face_recognition.face_encodings(face_image)[0]
        known_face_encodings.append(face_encoding)
        known_face_names.append(name)

# Function to take voice command
def takeCommand():
    r = sr.Recognizer()
    query = ""  # Initialize query with an empty string
    with sr.Microphone() as source:
        print('Listening....')
        r.pause_threshold = 1
        audio = r.listen(source)
        print("Audio captured:", audio)  # Add this line to print captured audio
    try:
        print("Recognizing.....")
        query = r.recognize_google(audio, language='en-in')
        print("saurav said: {} \n".format(query))
    except Exception as e:
        print("Say That Again....")
        
    return query

if __name__ == '__main__':
    # Directory path containing face images
    faces_directory = r"C:\Users\<USER>\OneDrive\Desktop\teacher\database"
    face_files = [f for f in os.listdir(faces_directory) if f.endswith(('.jpg', '.jpeg', '.png'))]
    known_face_encodings = []
    known_face_names = []

    for face_file in face_files:
        face_image = face_recognition.load_image_file(os.path.join(faces_directory, face_file))
        face_encoding = face_recognition.face_encodings(face_image)[0]
        face_name = os.path.splitext(face_file)[0]
        known_face_encodings.append(face_encoding)
        known_face_names.append(face_name)

    video_capture = cv2.VideoCapture(0)

    face_recognition_active = True  # Flag to control face recognition
    object_detection_active = False  # Flag to control object detection
    #object_detected = False    

    # Load emotion detection model
    emotion_json_file = open(r"C:\Users\<USER>\OneDrive\Desktop\teacher\facialemotionmodel.json", "r")
    emotion_model_json = emotion_json_file.read()
    emotion_json_file.close()
    emotion_model = model_from_json(emotion_model_json)
    emotion_model.load_weights(r"C:\Users\<USER>\OneDrive\Desktop\Dataintelliage\face recognition mod\models\facialemotionmodel.h5")

    # Load age and gender detection models
    faceProto=r"C:\Users\<USER>\OneDrive\Desktop\Dataintelliage\face recognition mod\models\opencv_face_detector.pbtxt"
    faceModel=r"C:\Users\<USER>\OneDrive\Desktop\Dataintelliage\face recognition mod\models\opencv_face_detector_uint8.pb"
    ageProto=r"C:\Users\<USER>\OneDrive\Desktop\Dataintelliage\face recognition mod\models\age_deploy.prototxt"
    ageModel=r"C:\Users\<USER>\OneDrive\Desktop\Dataintelliage\face recognition mod\models\age_net.caffemodel"
    genderProto=r"C:\Users\<USER>\OneDrive\Desktop\Dataintelliage\face recognition mod\models\gender_deploy.prototxt"
    genderModel=r"C:\Users\<USER>\OneDrive\Desktop\Dataintelliage\face recognition mod\models\gender_net.caffemodel"

    MODEL_MEAN_VALUES=(78.4263377603, 87.7689143744, 114.895847746)
    ageList=['(0-2)', '(4-6)', '(8-12)', '(15-20)', '(25-32)', '(38-43)', '(48-53)', '(60-100)']
    genderList=['Male','Female']

    faceNet=cv2.dnn.readNet(faceModel,faceProto)
    ageNet=cv2.dnn.readNet(ageModel,ageProto)
    genderNet=cv2.dnn.readNet(genderModel,genderProto)
    padding=20
    emotion_duration = 3  # Duration in seconds for persistent emotion display
    emotion_start_time = None  # Initialize emotion start time
    current_emotion = None
# Inside the main loop
    while True:
        ret, frame = video_capture.read()

        if object_detection_active:
            # Check if the user wants to stop object detection
            if detect_object(frame):
                object_detection_active = False
                speak("Deactivating object detection.")
                face_recognition_active = True
                continue   # Skip the rest of the loop and go to the next iteration

        if face_recognition_active:
            name = recognize_face(frame, known_face_encodings, known_face_names)

       


        # Detect if there is a face in the frame
        face_locations = face_recognition.face_locations(frame)
        
        if face_locations:  # Check if any face is detected
            # If faces are detected, perform face recognition
            name = recognize_face(frame, known_face_encodings, known_face_names)

            if name != 'unknown':
                # Perform age, gender, and emotion detection
                resultImg, faceBoxes = highlightFace(faceNet, frame)
                if faceBoxes:
                    for faceBox in faceBoxes:
                        face = frame[max(0,faceBox[1]-padding):min(faceBox[3]+padding,frame.shape[0]-1), max(0,faceBox[0]-padding):min(faceBox[2]+padding, frame.shape[1]-1)]
                        
                        # Detect age
                        ageNet.setInput(cv2.dnn.blobFromImage(face, 1.0, (227, 227), MODEL_MEAN_VALUES, swapRB=False))
                        agePreds = ageNet.forward()
                        age = ageList[agePreds[0].argmax()]

                        # Detect gender
                        genderNet.setInput(cv2.dnn.blobFromImage(face, 1.0, (227, 227), MODEL_MEAN_VALUES, swapRB=False))
                        genderPreds = genderNet.forward()
                        gender = genderList[genderPreds[0].argmax()]
                        
                        # Detect emotion
                        emotion_index = detect_emotion(face, emotion_model)
                        emotions = ['angry', 'disgust', 'fear', 'happy', 'neutral', 'sad', 'surprise']
                        detected_emotion = emotions[emotion_index]

                speak(f"Hello, how can I help you, {name}?")
                query = takeCommand().lower()
                if query:  # Check if query is not empty
                    ans = Reply(query)
                    print(ans)
                    speak(ans)

                if 'open youtube' in query:
                    webbrowser.open("www.youtube.com")
                if 'open google' in query:
                    webbrowser.open("www.google.com")
                if 'bye' in query:
                    break
                if 'hey what is this object' in query:
                    object_detection_active = True
                    face_recognition_active = False
                    continue

                if "please handshake" in query:
                    print("Initiating handshake...")
                    move_servo(150)  # Move servo to 45 degrees
                    print("Handshake completed!")
                    speak("myself tara, nice to meet you")  
                
                if "story mode" in query:
                    print('story mode activating')
                    
                   # Create an instance of StoryProcessor
                    processor = StoryProcessor()

                   # Call the main method to start the story processing
                    processor.main()

                if "picture mode" in query:
                    print('picture mode activating')
                    
                    capture_and_process_image()

               
                # Save conversation with recognized face name
                save_conversation(name, query, ans, detected_emotion, age, gender)
                cv2.imshow('Video', frame)

            elif name == 'unknown':
                handle_unknown_face(frame)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

        

    video_capture.release()
    cv2.destroyAllWindows()        
