import cv2
import face_recognition
import os
import csv
from datetime import datetime
import pyttsx3  # Importing the text-to-speech library

# Initialize variables to track recognized faces
recognized_faces = set()
absent_faces = set()  # To track absent faces

# Load known face encodings and names
known_face_encodings = []
known_face_names = []
for student_folder in os.listdir("students_data"):
    student_name = os.path.splitext(student_folder)[0]
    student_image = face_recognition.load_image_file("students_data/" + student_folder)
    student_encoding = face_recognition.face_encodings(student_image)[0]
    known_face_encodings.append(student_encoding)
    known_face_names.append(student_name)

# Initialize variables
face_locations = []
face_encodings = []
face_names = []
process_this_frame = True

# Open webcam
video_capture = cv2.VideoCapture(0)

# Create or append to the attendance CSV file
csv_filename = "attendance.csv"
with open(csv_filename, 'a', newline='') as csvfile:
    fieldnames = ['Name', 'Date', 'Present']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

    # Write header if the file is empty
    if os.stat(csv_filename).st_size == 0:
        writer.writeheader()

    while True:
        # Capture frame-by-frame
        ret, frame = video_capture.read()

        # Resize frame to speed up face recognition
        small_frame = cv2.resize(frame, (0, 0), fx=0.25, fy=0.25)

        # Convert the image from BGR color to RGB color
        rgb_small_frame = small_frame[:, :, ::-1]

        if process_this_frame:
            # Find all the faces and face encodings in the current frame
            face_locations = face_recognition.face_locations(rgb_small_frame)
            face_encodings = face_recognition.face_encodings(rgb_small_frame, face_locations)

            face_names = []
            for face_encoding in face_encodings:
                # See if the face matches any known faces
                matches = face_recognition.compare_faces(known_face_encodings, face_encoding)
                name = "Unknown"

                # Check if the face matches any known student
                if True in matches:
                    first_match_index = matches.index(True)
                    name = known_face_names[first_match_index]

                # Add the recognized face to the set of recognized faces
                recognized_faces.add(name)

                face_names.append(name)

            # Check for absent faces
            for known_name in known_face_names:
                if known_name not in face_names:
                    absent_faces.add(known_name)

            # Speak the names of absent students
            for absent_name in absent_faces:
                engine = pyttsx3.init()
                engine.say(f"{absent_name} is absent")
                engine.runAndWait()

            # Write attendance for present students
            for name in face_names:
                writer.writerow({'Name': name, 'Date': datetime.now().strftime("%Y-%m-%d"), 'Present': 'P'})

        process_this_frame = not process_this_frame

        # Display the results
        for (top, right, bottom, left), name in zip(face_locations, face_names):
            # Scale back up face locations
            top *= 4
            right *= 4
            bottom *= 4
            left *= 4

            # Draw a box around the face
            cv2.rectangle(frame, (left, top), (right, bottom), (0, 0, 255), 2)

            # Draw a label with a name below the face
            cv2.rectangle(frame, (left, bottom - 35), (right, bottom), (0, 0, 255), cv2.FILLED)
            font = cv2.FONT_HERSHEY_DUPLEX
            cv2.putText(frame, name, (left + 6, bottom - 6), font, 1.0, (255, 255, 255), 1)

        # Display the resulting image
        cv2.imshow('Video', frame)

        # Hit 'q' on the keyboard to quit
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

# Release handle to the webcam
video_capture.release()
cv2.destroyAllWindows()
