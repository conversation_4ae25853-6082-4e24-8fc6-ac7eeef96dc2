*This is Teacher Robot made by DATAINTELLIAGE.*


Steps to install this teacher robot.
1. install all dependencies 



2. main file for running teacher robot.




3. Teacher.py for all activity.




4. Teach_mode.py for teaching in class.



5. Picture_mode.py for explaining picture



6.Story_mode.py for story summary and generating image



7. pose_detection.py for pose detection



8. object_detection.py for object detection



9. sketch_may21a is C++ file for arm and hand motor motion



10.sketch_may18a is C++ file for head motor motion


normal_chat is json file
book is the pdf for teach mode