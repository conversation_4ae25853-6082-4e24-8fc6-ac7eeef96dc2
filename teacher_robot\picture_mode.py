import cv2
import textwrap
from PIL import Image
import google.generativeai as genai
from IPython.display import display, Markdown
import pyttsx3

import speech_recognition as sr

def capture_and_process_image():
    # Initialize text-to-speech engine
    engine = pyttsx3.init()
    voices = engine.getProperty('voices')
    rate = engine.getProperty('rate')
    engine.setProperty('voice', voices[1].id)
    engine.setProperty('rate', 120)

    def to_markdown(text):
        text = text.replace('•', '  *')
        return Markdown(textwrap.indent(text, '> ', predicate=lambda _: True))

    # Set up Google Generative AI
    GOOGLE_API_KEY = 'AIzaSyAHv-6xEbFE64mOuEtsSb39Q9M3YsDVxt4'
    genai.configure(api_key=GOOGLE_API_KEY)

    # Load generative model
    model = genai.GenerativeModel('gemini-pro-vision')

    # Initialize camera
    cap = cv2.VideoCapture(0)  # 0 for default camera

    while True:
        # Capture frame-by-frame
        ret, frame = cap.read()

        # Display the captured frame 
        cv2.imshow('Frame', frame)

        # Wait for key press
        key = cv2.waitKey(1) & 0xFF

        # If 's' is pressed, capture the image and process it
        if key == ord('s'):
            # Save the captured image
            cv2.imwrite('captured_image.jpg', frame)

            # Load the captured image
            img = Image.open('captured_image.jpg')
            display(img)

            # Perform object detection using your generative model
            response = model.generate_content(img)
            print(response.text)
            display(to_markdown(response.text))

            engine.say(response.text)
            engine.runAndWait()

            # Release the camera and close all OpenCV windows
            cap.release()
            cv2.destroyAllWindows()

            return response.text

        # If 'q' is pressed, quit
        elif key == ord('q'):
            break

        # Speech recognition
        recognizer = sr.Recognizer()
        with sr.Microphone() as source:
            print("Picture Listening...")
            audio = recognizer.listen(source)

            try:
                text = recognizer.recognize_google(audio)
                if text.lower() == "deactivate picture mode":
                    return True  # Signal to stop object detection
            except sr.UnknownValueError:
                print("Speech Recognition could not understand audio")
            except sr.RequestError as e:
                print("Could not request results from Speech Recognition service; {0}".format(e))




