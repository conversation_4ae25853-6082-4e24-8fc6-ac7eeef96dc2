#include <Servo.h>

Servo myServo;  // Create a Servo object
int servoPin = 9;  // Pin to which the servo is connected
int currentAngle = 90;  // Initialize the current angle to the center position

void setup() {
  myServo.attach(servoPin);  // Attach the servo on the specified pin
  Serial.begin(9600);  // Start serial communication at 9600 baud rate
  myServo.write(currentAngle);  // Initialize the servo to the middle position
  Serial.println("Servo ready, send angle between 0 and 180");
}

void loop() {
  if (Serial.available() > 0) {
    int angle = Serial.parseInt();  // Read the angle value from the serial input
    Serial.print("Received angle: ");
    Serial.println(angle);

    if (angle >= 30 && angle <= 120) {
      // Reverse the angle calculation
      currentAngle = 180 - angle;  // Update the current angle
      myServo.write(currentAngle);  // Move the servo to the new angle
      Serial.print("Moving servo to ");
      Serial.print(currentAngle);
      Serial.println(" degrees");
    } else {
      Serial.println("Invalid angle. Please send a value between 0 and 180.");
    }
  }
}
