import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from nltk.probability import FreqDist
from nltk.tokenize.treebank import TreebankWordDetokenizer
from langchain_community.document_loaders import PyPDFLoader
from langchain_community.vectorstores import Chroma
from langchain_openai import OpenAIEmbeddings
import pyttsx3
import speech_recognition as sr

def run_teacher(pdf_file):
    # Initialize text-to-speech engine
    engine = pyttsx3.init()
    voices = engine.getProperty('voices')
    engine.setProperty('voice', voices[1].id)
    engine.setProperty('rate', 120)

    # Check if NLTK resources are already downloaded
    nltk_resources_exist = nltk.data.find('tokenizers/punkt') is not None and nltk.data.find('corpora/stopwords') is not None
    if not nltk_resources_exist:
        nltk.download('punkt')
        nltk.download('stopwords')

    # Load PDF and create vector store
    def load_and_create_vector_store(pdf_file):
        loader = PyPDFLoader(pdf_file)
        pages = loader.load_and_split()
        embeddings = OpenAIEmbeddings()
        store = Chroma.from_documents(pages, embeddings, collection_name=pdf_file)
        return store

    # Process user input to search for relevant content in the PDF
    def process_user_input(query, store):
        # Find most relevant content in the PDF based on the user's question
        search_results = store.similarity_search_with_score(query)
        if search_results:
            # Get the most relevant page content
            most_relevant_page_content = search_results[0][0].page_content
            
            # Extract a detailed and conclusive summary explanation
            summary = generate_conclusive_summary(most_relevant_page_content)
            response = f"i think :\n{summary}"
        else:
            response = "No relevant content found in the PDF for your question."

        return response

    def is_wake_word(user_input):
        wake_word = "teacher"
        return wake_word in user_input


    # Function to generate a detailed summary explanation
    def generate_conclusive_summary(content):
        # Tokenize the content into sentences
        sentences = sent_tokenize(content)
        
        # Tokenize each sentence into words and filter out stopwords
        words = word_tokenize(content.lower())
        stopwords_list = set(stopwords.words('english'))
        filtered_words = [word for word in words if word.isalnum() and word not in stopwords_list]
        
        # Calculate word frequency distribution
        freq_dist = FreqDist(filtered_words)
        
        # Identify top keywords based on word frequency
        top_keywords = [word for word, freq in freq_dist.most_common(5)]  # Extract top 5 keywords
        
        # Generate a summary by selecting sentences containing the top keywords
        summary_sentences = []
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in top_keywords):
                summary_sentences.append(sentence)
        
        # Join summary sentences into a coherent summary
        summary = TreebankWordDetokenizer().detokenize(summary_sentences)
        
        return summary

    def speak(text):
        engine.say(text)
        engine.runAndWait()

    def takeCommand():
        r = sr.Recognizer()
        query = ""  # Initialize query with an empty string
        with sr.Microphone() as source:
            print('Listening....')
            r.pause_threshold = 1
            audio = r.listen(source)
            print("Audio captured:", audio)  # Add this line to print captured audio
        try:
            print("Recognizing.....")
            query = r.recognize_google(audio, language='en-in')
            print("saurav said: {} \n".format(query))
        except Exception as e:
            print("Say That Again....")
            
        return query       # Other functions as before...

    def main():
        # Load PDF and create vector store
        pdf_file = 'book.pdf'
    
    # Load PDF and create vector store
   
        store = load_and_create_vector_store(pdf_file)
        
        is_awake = False
        
        while True:
            if not is_awake:
                user_input = takeCommand().lower()
                if user_input and is_wake_word(user_input):
                    speak("Waking up. Please specify the topic.")
                    is_awake = True
                    continue
            
            if is_awake:
                speak("What topic would you like to learn about?")
                topic_query = takeCommand().lower()
                
                if topic_query:
                    response = process_user_input(topic_query, store)
                    print(response)
                    speak(response)
                    
                    speak("Going back to sleep.")
                    is_awake = False
            
            if is_awake and query.lower() == 'exit':
                print("Exiting...")
                break

    main()

# Add a check for __name__ == '__main__' to run the assistant directly if needed

    #pdf_file_path = r'C:\Users\<USER>\face recognition mod\book.pdf'  # Update with your PDF file path
    
if __name__ == '__main__':
    pdf_path = 'book.pdf'
    #run_teacher(pdf_path)