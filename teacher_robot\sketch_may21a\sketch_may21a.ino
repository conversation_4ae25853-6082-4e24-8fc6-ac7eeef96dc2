#include <Servo.h>

// First motor variables
Servo motor;
const int motorPin = 9;
const int initialDegree = 120; // Initial position at 120 degrees
const int targetDegree = 20; // Adjust this value according to your desired target position
const int motorRestDegree = 120; // Rest position after move
const int delayTime = 5000; // 5 seconds
const int moveStep = 1; // Step size for smooth motion

// Second motor variables
Servo myServo;
const int servoPin = 10;  // Pin to which the second servo motor is connected

void setup() {
  Serial.begin(9600);
  motor.attach(motorPin);
  myServo.attach(servoPin);

  // Set motors to initial positions
  motor.write(initialDegree);
  myServo.write(0);
  delay(1000); // Wait for the servos to reach the initial positions

  Serial.println("Setup complete");
}

void moveMotor(int initialDegree, int targetDegree) {
  if (initialDegree < targetDegree) {
    for (int pos = initialDegree; pos <= targetDegree; pos += moveStep) {
      motor.write(pos);
      delay(15); // Adjust this value for smoother/faster motion
    }
  } else {
    for (int pos = initialDegree; pos >= targetDegree; pos -= moveStep) {
      motor.write(pos);
      delay(15); // Adjust this value for smoother/faster motion
    }
  }
}

void moveSecondMotor() {
  int secondMotorStep = 180 / (2000 / 15); // Steps to complete movement in 2 seconds each way
  
  // Move second motor from 180 to 0 degrees
  for (int pos = 180; pos >= 0; pos -= secondMotorStep) {
    myServo.write(pos);
    delay(15);
  }
  // Move second motor from 0 back to 180 degrees
  for (int pos = 0; pos <= 180; pos += secondMotorStep) {
    myServo.write(pos);
    delay(15);
  }
}

void loop() {
  if (Serial.available() > 0) {
    char receivedChar = Serial.read();
    if (receivedChar == 'H') {
      // Move first motor to target position
      Serial.println("Moving first motor to target position");
      moveMotor(initialDegree, targetDegree);
      
      // While the first motor is at the target position, move the second motor
      Serial.println("Moving second motor from 180 to 0 and back to 180 degrees");
      moveSecondMotor();  // Move the second motor from 180 to 0 degrees and back to 180
      
      delay(delayTime - 4000); // Remaining delay time for first motor to complete 5 seconds

      // Return first motor to initial position
      Serial.println("Returning first motor to initial position");
      moveMotor(targetDegree, motorRestDegree);
      delay(delayTime);

      Serial.println("Cycle complete");
    }
  }
}
