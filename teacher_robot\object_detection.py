import cv2
import numpy as np
import pyttsx3
import time
import speech_recognition as sr

# Load class names
classNames = []
classFile = r'C:\Users\<USER>\OneDrive\Desktop\Everything\teacher\teacher_robot\models\coco.names'
with open(classFile, 'rt') as f:
    classNames = f.read().rstrip('\n').split('\n')

# Load pre-trained model
configPath = r'C:\Users\<USER>\OneDrive\Desktop\Everything\teacher\teacher_robot\models\ssd_mobilenet_v3_large_coco_2020_01_14.pbtxt'
weightsPath = r'C:\Users\<USER>\OneDrive\Desktop\Everything\teacher\teacher_robot\models\frozen_inference_graph.pb'
net = cv2.dnn_DetectionModel(weightsPath, configPath)
net.setInputSize(320, 320)
net.setInputScale(1.0/127.5)
net.setInputMean((127.5, 127.5, 127.5))
net.setInputSwapRB(True)

# Initialize pyttsx3 TTS engine
engine = pyttsx3.init()

prev_time = time.time()
object_detected = False
object_name = ""

def speak(text):
    engine.say(text)
    engine.runAndWait()

def detect_object(frame):
    global object_detected
    global object_name
    global prev_time

    classIds, confs, bbox = net.detect(frame, confThreshold=0.5)

    if len(classIds) != 0:
        for classId, confidence, box in zip(classIds.flatten(), confs.flatten(), bbox):
            if classNames[classId-1] != 'persona':
                cv2.rectangle(frame, box, color=(0, 255, 0), thickness=3)
                cv2.putText(frame, classNames[classId-1], (box[0]+10, box[1]+30), cv2.FONT_HERSHEY_COMPLEX, 1, (0, 255, 0), 2)
                if not object_detected:
                    object_detected = True
                    object_name = classNames[classId-1]
                    prev_time = time.time()

    if object_detected:
        current_time = time.time()
        if current_time - prev_time >= 3:
            print(f"Detected object: {object_name}")
            speak(f"I see a {object_name}")  # Speak the object name
            object_detected = False
            object_name = ""

    cv2.imshow('Object Detection', frame)

    # Speech recognition
    recognizer = sr.Recognizer()
    with sr.Microphone() as source:
        print("Object Listening...")
        audio = recognizer.listen(source)

        try:
            text = recognizer.recognize_google(audio)
            if text.lower() == "ok thank you":
                return True  # Signal to stop object detection
        except sr.UnknownValueError:
            print("Speech Recognition could not understand audio")
        except sr.RequestError as e:
            print("Could not request results from Speech Recognition service; {0}".format(e))

    return False
