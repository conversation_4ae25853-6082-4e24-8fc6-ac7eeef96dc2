import threading
import subprocess

# Function to run face_recognition_script.py
def run_face_recognition_script():
    subprocess.run(["python", "newface.py"])

# Function to run object_detection_script.py
def run_object_detection_script():
    subprocess.run(["python", "webmy1.py"])

if __name__ == "__main__":
    # Create threads for each script
    face_recognition_thread = threading.Thread(target=run_face_recognition_script)
    object_detection_thread = threading.Thread(target=run_object_detection_script)

    # Start the threads
    face_recognition_thread.start()
    object_detection_thread.start()

    # Wait for both threads to complete
    face_recognition_thread.join()
    object_detection_thread.join()
