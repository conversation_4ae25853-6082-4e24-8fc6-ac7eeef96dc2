import os
import time
import speech_recognition as sr
import openai
import requests
import pyttsx3
import io
import tkinter as tk
from PIL import Image, ImageTk

class StoryProcessor:
    def __init__(self):
        pass

    # Step 1: Speech Recognition
    def listen_to_story(self):  
        recognizer = sr.Recognizer()
        with sr.Microphone() as source:
            print("Listening to your story...")
            recognizer.adjust_for_ambient_noise(source)  # Adjust for ambient noise before listening
            start_time = time.time()
            while time.time() - start_time < 20:  # Listen for at most 60 seconds
                audio = recognizer.listen(source, timeout=20)  # Listen for up to 10 seconds
                try:
                    story_text = recognizer.recognize_google(audio)
                    print("You said:", story_text)
                    if "finished" in story_text.lower():
                        break  # Stop listening if "finished" is said
                except sr.UnknownValueError:
                    print("Sorry, I could not understand your audio.")
                except sr.RequestError as e:
                    print(f"Could not request results from Google Speech Recognition service; {e}")

        return story_text


    def fetch_image(self, url):
        """ Fetches an image from a URL and returns a PIL image object. """
        response = requests.get(url)
        image = Image.open(io.BytesIO(response.content))
        return image

    # Step 2: OpenAI GPT for Summarization
    def summarize_story(self, story_text):
        openai.api_key = '***************************************************'
        response = openai.Completion.create(
            engine="gpt-3.5-turbo-instruct",
            prompt="Summarize this: " + story_text,
            max_tokens=200
        )
        summary = response.choices[0].text.strip()
        return summary

    # Step 3: DALL-E for Image Generation (Hypothetical example)
    def generate_image(self, summary):
        """ Generates an image from OpenAI's DALL-E based on a given prompt. """
        api_key = '***************************************************'  # Hardcoded API key, replace YOUR_ACTUAL_OPENAI_API_KEY with your actual key
        if not api_key:
            print("API key not found. Please check your API key.")
            return None

        # API endpoint for generating images
        url = "https://api.openai.com/v1/images/generations"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "model": "dall-e-2",
            "prompt": summary,
            "n": 1,
            "size": "1024x1024"
        }

        response = requests.post(url, headers=headers, json=data)
        response_data = response.json()

        # Debugging: Print the entire JSON response
        print("Response JSON:", response_data)

        try:
            # Adjust the following line according to the actual JSON response structure
            image_url = response_data['data'][0]['url']
            print("Generated Image URL:", image_url)
            return self.fetch_image(image_url)
        except KeyError as e:
            print(f"KeyError: {e} - Check the JSON structure above and adjust the key names accordingly.")
            return None

    # Step 4: Text-to-Speech (TTS) with pyttsx3
    def speak_text(self, text):
        engine = pyttsx3.init()
        engine.say(text)
        engine.runAndWait()

    def display_image(self, image):
        """ Displays a PIL image object in a Tkinter window. """
        root = tk.Tk()
        root.title("Generated Image")

        tk_image = ImageTk.PhotoImage(image)
        label = tk.Label(root, image=tk_image)
        label.image = tk_image  # Keep a reference to avoid garbage collection
        label.pack()

        root.mainloop()


    # Main function to orchestrate the process
    def main(self):
        story_text = self.listen_to_story()
        if story_text:
            summary = self.summarize_story(story_text)
            print("Summary:", summary)
            self.speak_text(summary)
            image = self.generate_image(summary)
            if image:
                self.display_image(image)
        else:
            print("Could not understand the audio.")

# This part ensures that if the script is run directly, the main() function will be executed.
if __name__ == "__main__":
    story_processor = StoryProcessor()
    story_processor.main()
